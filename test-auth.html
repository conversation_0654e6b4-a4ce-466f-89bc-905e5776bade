<!DOCTYPE html>
<html>
<head>
    <title>Test Google OAuth</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Google OAuth Test</h1>
    
    <div class="section">
        <h2>Step 1: Setup Instructions</h2>
        <ol>
            <li>Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
            <li>Create a new project or select existing one</li>
            <li>Enable "Google Photos Library API"</li>
            <li>Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"</li>
            <li>Choose "Chrome Extension" as application type</li>
            <li>Add your extension ID to authorized origins</li>
            <li>Copy the Client ID and update your manifest.json</li>
        </ol>
    </div>

    <div class="section">
        <h2>Step 2: Test Token Validation</h2>
        <p>Enter a Google OAuth access token to test if your backend can validate it:</p>
        <input type="text" id="tokenInput" placeholder="Paste Google OAuth access token here" style="width: 100%; padding: 10px;">
        <br><br>
        <button onclick="testToken()">Test Token</button>
        <button onclick="testBackendConnection()">Test Backend Connection</button>
        <div id="tokenResult"></div>
    </div>

    <div class="section">
        <h2>Step 3: Extension Testing</h2>
        <p>To test your Chrome extension:</p>
        <ol>
            <li>Update the Client ID in manifest.json</li>
            <li>Load the extension in Chrome (Developer mode)</li>
            <li>Go to Google Photos</li>
            <li>Try clicking the compress button on a video</li>
            <li>Check the browser console for any errors</li>
        </ol>
    </div>

    <script>
        async function testToken() {
            const token = document.getElementById('tokenInput').value.trim();
            const resultDiv = document.getElementById('tokenResult');
            
            if (!token) {
                resultDiv.innerHTML = '<p class="error">Please enter a token</p>';
                return;
            }

            resultDiv.innerHTML = '<p class="info">Testing token...</p>';

            try {
                // Test Google's token validation endpoint
                const googleResponse = await fetch(`https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${token}`);
                const googleData = await googleResponse.json();
                
                if (googleResponse.ok) {
                    resultDiv.innerHTML = `
                        <p class="success">✓ Token is valid with Google</p>
                        <pre>${JSON.stringify(googleData, null, 2)}</pre>
                    `;
                    
                    // Test your backend
                    try {
                        const backendResponse = await fetch('http://localhost:5000/api/videos', {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (backendResponse.ok) {
                            const backendData = await backendResponse.json();
                            resultDiv.innerHTML += `
                                <p class="success">✓ Backend accepted the token</p>
                                <pre>${JSON.stringify(backendData, null, 2)}</pre>
                            `;
                        } else {
                            const errorData = await backendResponse.json();
                            resultDiv.innerHTML += `
                                <p class="error">✗ Backend rejected the token (${backendResponse.status})</p>
                                <pre>${JSON.stringify(errorData, null, 2)}</pre>
                            `;
                        }
                    } catch (backendError) {
                        resultDiv.innerHTML += `
                            <p class="error">✗ Backend connection failed: ${backendError.message}</p>
                        `;
                    }
                } else {
                    resultDiv.innerHTML = `
                        <p class="error">✗ Token is invalid</p>
                        <pre>${JSON.stringify(googleData, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
            }
        }

        async function testBackendConnection() {
            const resultDiv = document.getElementById('tokenResult');
            resultDiv.innerHTML = '<p class="info">Testing backend connection...</p>';

            try {
                const response = await fetch('http://localhost:5000/api/videos', {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.status === 401) {
                    resultDiv.innerHTML = '<p class="success">✓ Backend is running (returned 401 as expected without token)</p>';
                } else {
                    resultDiv.innerHTML = `<p class="info">Backend responded with status: ${response.status}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">✗ Backend connection failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
