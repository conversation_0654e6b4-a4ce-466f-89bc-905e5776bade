using CasCap.Services;
using CasCap.Models;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace VidCompressor.Services;

public class GooglePhotosService
{
    private readonly CasCap.Services.GooglePhotosService _googlePhotosService;

    public GooglePhotosService(ILogger<CasCap.Services.GooglePhotosService> logger, IOptions<GooglePhotosOptions> options, IHttpClientFactory httpClientFactory)
    {
        var httpClient = httpClientFactory.CreateClient();
        _googlePhotosService = new CasCap.Services.GooglePhotosService(logger, options, httpClient);
    }

    public async Task<List<string>> GetVideosAsync(string accessToken)
    {
        // Note: The CasCap library handles authentication differently
        // You'll need to configure OAuth properly through the GooglePhotosOptions
        if (!await _googlePhotosService.LoginAsync())
        {
            throw new InvalidOperationException("Failed to authenticate with Google Photos");
        }

        var videos = new List<string>();
        await foreach (var item in _googlePhotosService.GetMediaItemsAsync())
        {
            if (item.mediaMetadata?.video != null)
            {
                videos.Add(item.id);
            }
        }

        return videos;
    }

    public async Task<VideoInfo> GetVideoInfoAsync(string accessToken, string mediaItemId)
    {
        if (!await _googlePhotosService.LoginAsync())
        {
            throw new InvalidOperationException("Failed to authenticate with Google Photos");
        }

        var mediaItem = await _googlePhotosService.GetMediaItemByIdAsync(mediaItemId);
        if (mediaItem == null)
        {
            throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
        }

        var videoMetadata = mediaItem.mediaMetadata?.video;
        if (videoMetadata == null)
        {
            throw new InvalidOperationException($"Media item {mediaItemId} is not a video");
        }

        // Calculate file size estimate (Google Photos API doesn't always provide exact file size)
        // We'll use dimensions and duration to estimate
        // Note: CasCap library Video properties might be named differently
        var width = 1920; // Default values since we need to check actual property names
        var height = 1080;
        var fps = 30.0;
        var durationSeconds = 0.0;

        // Try to get actual values from video metadata if properties exist
        try
        {
            // These property names might need adjustment based on actual CasCap library structure
            var widthProperty = videoMetadata.GetType().GetProperty("Width") ?? videoMetadata.GetType().GetProperty("width");
            var heightProperty = videoMetadata.GetType().GetProperty("Height") ?? videoMetadata.GetType().GetProperty("height");
            var fpsProperty = videoMetadata.GetType().GetProperty("Fps") ?? videoMetadata.GetType().GetProperty("fps");
            var durationProperty = videoMetadata.GetType().GetProperty("Duration") ?? videoMetadata.GetType().GetProperty("duration");

            if (widthProperty != null)
                width = (int)(widthProperty.GetValue(videoMetadata) ?? 1920);
            if (heightProperty != null)
                height = (int)(heightProperty.GetValue(videoMetadata) ?? 1080);
            if (fpsProperty != null)
                fps = Convert.ToDouble(fpsProperty.GetValue(videoMetadata) ?? 30.0);

            if (durationProperty != null)
            {
                var durationValue = durationProperty.GetValue(videoMetadata)?.ToString();
                if (!string.IsNullOrEmpty(durationValue))
                {
                    var durationStr = durationValue.TrimEnd('s');
                    double.TryParse(durationStr, out durationSeconds);
                }
            }
        }
        catch
        {
            // Use default values if reflection fails
            width = 1920;
            height = 1080;
            fps = 30.0;
            durationSeconds = 60.0; // Default 1 minute
        }

        // Rough estimate: assume ~8 Mbps for 1080p video
        var estimatedBitrate = (width * height * fps) / 250000.0; // Very rough approximation
        var estimatedSizeBytes = (long)(estimatedBitrate * durationSeconds * 125000); // Convert Mbps to bytes

        return new VideoInfo
        {
            MediaItemId = mediaItemId,
            Width = width,
            Height = height,
            Duration = durationSeconds,
            EstimatedSizeBytes = estimatedSizeBytes,
            Filename = mediaItem.filename ?? "video.mp4"
        };
    }

    public async Task<Stream> DownloadVideoAsync(string accessToken, string mediaItemId)
    {
        if (!await _googlePhotosService.LoginAsync())
        {
            throw new InvalidOperationException("Failed to authenticate with Google Photos");
        }

        var mediaItem = await _googlePhotosService.GetMediaItemByIdAsync(mediaItemId);
        if (mediaItem == null)
        {
            throw new InvalidOperationException($"Media item with ID {mediaItemId} not found");
        }

        // Download the video using the DownloadBytes method from CasCap library
        var videoBytes = await _googlePhotosService.DownloadBytes(mediaItem, downloadVideoBytes: true);
        return new MemoryStream(videoBytes ?? throw new InvalidOperationException("Failed to download video bytes"));
    }

    public async Task UploadVideoAsync(string accessToken, string filePath)
    {
        if (!await _googlePhotosService.LoginAsync())
        {
            throw new InvalidOperationException("Failed to authenticate with Google Photos");
        }

        await _googlePhotosService.UploadSingle(filePath);
    }
}

public class VideoInfo
{
    public string MediaItemId { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public double Duration { get; set; }
    public long EstimatedSizeBytes { get; set; }
    public string Filename { get; set; } = string.Empty;
}
