using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StackExchange.Redis;
using System.Threading.Tasks;
using VidCompressor.Services;
using System.Net.Http;
using System.Text.Json;

[ApiController]
[Route("api/[controller]")]
public class VideosController : ControllerBase
{
    private readonly GooglePhotosService _googlePhotosService;
    private readonly IConnectionMultiplexer _redis;
    private readonly HttpClient _httpClient;

    public VideosController(GooglePhotosService googlePhotosService, IConnectionMultiplexer redis, HttpClient httpClient)
    {
        _googlePhotosService = googlePhotosService;
        _redis = redis;
        _httpClient = httpClient;
    }

    private async Task<bool> ValidateGoogleTokenAsync(string accessToken)
    {
        try
        {
            var response = await _httpClient.GetAsync($"https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={accessToken}");
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var tokenInfo = JsonSerializer.Deserialize<JsonElement>(content);

                // Check if token has required scope
                if (tokenInfo.TryGetProperty("scope", out var scope))
                {
                    return scope.GetString()?.Contains("photoslibrary.readonly") == true;
                }
            }
            return false;
        }
        catch
        {
            return false;
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetVideos()
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

        if (string.IsNullOrEmpty(accessToken) || !await ValidateGoogleTokenAsync(accessToken))
        {
            return Unauthorized(new { message = "Invalid or expired access token" });
        }

        var videos = await _googlePhotosService.GetVideosAsync(accessToken);
        return Ok(videos);
    }

    [HttpGet("{mediaItemId}/info")]
    public async Task<IActionResult> GetVideoInfo(string mediaItemId)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

        if (string.IsNullOrEmpty(accessToken) || !await ValidateGoogleTokenAsync(accessToken))
        {
            return Unauthorized(new { message = "Invalid or expired access token" });
        }

        var videoInfo = await _googlePhotosService.GetVideoInfoAsync(accessToken, mediaItemId);
        return Ok(videoInfo);
    }

    [HttpGet("{mediaItemId}/download")]
    public async Task<IActionResult> DownloadVideo(string mediaItemId)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

        if (string.IsNullOrEmpty(accessToken) || !await ValidateGoogleTokenAsync(accessToken))
        {
            return Unauthorized(new { message = "Invalid or expired access token" });
        }

        var videoStream = await _googlePhotosService.DownloadVideoAsync(accessToken, mediaItemId);
        return File(videoStream, "video/mp4", "video.mp4");
    }

    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressVideo(string mediaItemId, [FromBody] CompressVideoRequest request)
    {
        var accessToken = HttpContext.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

        if (string.IsNullOrEmpty(accessToken) || !await ValidateGoogleTokenAsync(accessToken))
        {
            return Unauthorized(new { message = "Invalid or expired access token" });
        }

        var quality = request?.Quality ?? "medium";
        var subscriber = _redis.GetSubscriber();
        await subscriber.PublishAsync("video-compression-jobs", $"{accessToken}:{mediaItemId}:{quality}");
        return Ok(new { message = "Video compression started" });
    }
}

public class CompressVideoRequest
{
    public string Quality { get; set; } = "medium";
}
}

